import React from "react";
import { OverflowContainer } from "../lib/overflow-container";
import { Badge } from "./components/ui/badge";

// Example data for vertical overflow demonstration
const verticalItems = [
  "First Item",
  "Second Item", 
  "Third Item",
  "Fourth Item",
  "Fifth Item",
  "Sixth Item",
  "Seventh Item",
  "Eighth Item",
  "Ninth Item",
  "Tenth Item"
];

const horizontalTags = [
  "#react", "#typescript", "#overflow", "#container", "#vertical",
  "#horizontal", "#component", "#ui", "#library", "#demo"
];

export const VerticalExample = () => {
  return (
    <div style={{ padding: "20px", display: "flex", gap: "40px" }}>
      {/* Horizontal Example (Original behavior) */}
      <div style={{ flex: 1 }}>
        <h3 style={{ marginBottom: "16px" }}>Horizontal Overflow (Default)</h3>
        <div style={{ 
          width: "300px", 
          border: "1px solid #ccc", 
          padding: "16px",
          borderRadius: "8px"
        }}>
          <OverflowContainer
            renderHiddenElements={(hidden, hiddenIndexes) => (
              <Badge variant="outline">+{hidden.length} more</Badge>
            )}
            gap={8}
          >
            {horizontalTags.map(tag => (
              <Badge key={tag}>{tag}</Badge>
            ))}
          </OverflowContainer>
        </div>
      </div>

      {/* Vertical Example (New functionality) */}
      <div style={{ flex: 1 }}>
        <h3 style={{ marginBottom: "16px" }}>Vertical Overflow (New)</h3>
        <div style={{ 
          height: "200px", 
          border: "1px solid #ccc", 
          padding: "16px",
          borderRadius: "8px"
        }}>
          <OverflowContainer
            orientation="vertical"
            renderHiddenElements={(hidden, hiddenIndexes) => (
              <div style={{ 
                padding: "8px 12px", 
                backgroundColor: "#f0f0f0", 
                borderRadius: "4px",
                fontSize: "14px",
                color: "#666"
              }}>
                +{hidden.length} more items
              </div>
            )}
            gap={8}
          >
            {verticalItems.map(item => (
              <div 
                key={item}
                style={{
                  padding: "8px 12px",
                  backgroundColor: "#e3f2fd",
                  borderRadius: "4px",
                  border: "1px solid #bbdefb"
                }}
              >
                {item}
              </div>
            ))}
          </OverflowContainer>
        </div>
      </div>
    </div>
  );
};

export default VerticalExample;
